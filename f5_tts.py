from f5_tts.src.f5tts_api import F5TTSWrapper
import tempfile
import shutil
import os
import soundfile as sf
from typing import Optional, <PERSON><PERSON>

def synthesize_speech_with_f5tts(
    tts_wrapper,  # Instance of F5TTSWrapper
    ref_audio_file,  # File-like object (e.g., from UploadFile.file or open('audio.wav', 'rb'))
    ref_text: Optional[str],
    gen_text: str,
    model_type: str = "F5-TTS_v1",
    remove_silence: bool = False,
    seed: int = -1,
    cross_fade_duration: float = 0.15,
    nfe_step: int = 32,
    speed: float = 1.0,
    show_info=print
) -> str:
    """
    Performs F5-TTS inference and returns path to generated audio.

    Args:
        tts_wrapper: An instance of F5TTSWrapper
        ref_audio_file: File-like object (binary stream)
        ref_text: Optional reference text
        gen_text: Text to synthesize
        model_type: TTS model version
        remove_silence: Whether to remove silence from output
        seed: Seed value (-1 for random)
        cross_fade_duration: Duration of cross-fade between segments
        nfe_step: Denoising steps
        speed: Speed multiplier
        show_info: Logger function (default is `print`)

    Returns:
        Path to output .wav audio file
    """
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_audio:
            shutil.copyfileobj(ref_audio_file, tmp_audio)
            tmp_audio_path = tmp_audio.name
            show_info(f"[synthesize] Saved ref audio to {tmp_audio_path}")

        (sample_rate, audio_data), spectrogram_path, processed_ref_text, used_seed = tts_wrapper.infer(
            ref_audio_orig=tmp_audio_path,
            ref_text=ref_text,
            gen_text=gen_text,
            model_type=model_type,
            remove_silence=remove_silence,
            seed=seed,
            cross_fade_duration=cross_fade_duration,
            nfe_step=nfe_step,
            speed=speed,
            show_info=show_info,
        )

        out_path = tempfile.NamedTemporaryFile(delete=False, suffix=".wav").name
        sf.write(out_path, audio_data, sample_rate)
        show_info(f"[synthesize] Output audio saved to: {out_path}")

        os.unlink(tmp_audio_path)
        return out_path

    except Exception as e:
        show_info(f"[synthesize] Error: {str(e)}")
        if 'tmp_audio_path' in locals():
            try:
                os.unlink(tmp_audio_path)
            except:
                pass
        raise


