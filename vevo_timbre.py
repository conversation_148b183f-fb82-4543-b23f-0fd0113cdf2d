import os
import shutil
import torch
import tempfile
import time
from typing import Optional, Union
from huggingface_hub import snapshot_download
from models.vc.vevo.vevo_utils import VevoInferencePipeline, save_audio

# Global variables for model initialization
_inference_pipeline = None
_device = None

def _initialize_vevo_pipeline():
    """Initialize the Vevo inference pipeline if not already initialized."""
    global _inference_pipeline, _device
    
    if _inference_pipeline is not None:
        return _inference_pipeline
    
    # ===== Device =====
    _device = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")
    
    # ===== Model Setup =====
    tokenizer_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["tokenizer/vq8192/*"],
    )
    tokenizer_ckpt_path = os.path.join(tokenizer_dir, "tokenizer/vq8192")

    fmt_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["acoustic_modeling/Vq8192ToMels/*"],
    )
    fmt_cfg_path = "./models/vc/vevo/config/Vq8192ToMels.json"
    fmt_ckpt_path = os.path.join(fmt_dir, "acoustic_modeling/Vq8192ToMels")

    vocoder_dir = snapshot_download(
        repo_id="amphion/Vevo",
        repo_type="model",
        cache_dir="./ckpts/Vevo",
        allow_patterns=["acoustic_modeling/Vocoder/*"],
    )
    vocoder_cfg_path = "./models/vc/vevo/config/Vocoder.json"
    vocoder_ckpt_path = os.path.join(vocoder_dir, "acoustic_modeling/Vocoder")

    # ===== Inference Pipeline Init =====
    _inference_pipeline = VevoInferencePipeline(
        content_style_tokenizer_ckpt_path=tokenizer_ckpt_path,
        fmt_cfg_path=fmt_cfg_path,
        fmt_ckpt_path=fmt_ckpt_path,
        vocoder_cfg_path=vocoder_cfg_path,
        vocoder_ckpt_path=vocoder_ckpt_path,
        device=_device,
    )
    
    return _inference_pipeline


def vevo_timbre_transfer(
    source_audio_file,  # File-like object (e.g., from UploadFile.file or open('audio.wav', 'rb'))
    reference_audio_file,  # File-like object (e.g., from UploadFile.file or open('audio.wav', 'rb'))
    flow_matching_steps: int = 32,
    show_info=print
) -> str:
    """
    Perform timbre transfer using Vevo model.
    
    Args:
        source_audio_file: File-like object containing source audio (binary stream)
        reference_audio_file: File-like object containing reference audio (binary stream)
        flow_matching_steps: Number of flow matching steps for inference
        show_info: Logger function (default is `print`)
    
    Returns:
        str: Path to the output audio file
    """
    try:
        # Initialize pipeline if needed
        pipeline = _initialize_vevo_pipeline()
        show_info(f"[vevo_timbre] Pipeline initialized on device: {_device}")
        
        # Create temporary files for input audio
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as src_temp, \
             tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as ref_temp:
            
            # Copy uploaded files to temporary files
            shutil.copyfileobj(source_audio_file, src_temp)
            shutil.copyfileobj(reference_audio_file, ref_temp)
            
            src_path = src_temp.name
            ref_path = ref_temp.name
            show_info(f"[vevo_timbre] Saved source audio to: {src_path}")
            show_info(f"[vevo_timbre] Saved reference audio to: {ref_path}")
        
        # Create output file
        out_temp = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        out_path = out_temp.name
        out_temp.close()
        
        # Perform inference
        start_time = time.time()
        show_info(f"[vevo_timbre] Starting timbre transfer with {flow_matching_steps} flow matching steps...")
        
        gen_audio = pipeline.inference_fm(
            src_wav_path=src_path,
            timbre_ref_wav_path=ref_path,
            flow_matching_steps=flow_matching_steps,
        )
        save_audio(gen_audio, output_path=out_path)
        
        end_time = time.time()
        latency = end_time - start_time
        show_info(f"[vevo_timbre] Timbre transfer completed in {latency:.2f} seconds")
        show_info(f"[vevo_timbre] Output saved to: {out_path}")
        
        # Clean up temporary input files
        os.unlink(src_path)
        os.unlink(ref_path)
        
        return out_path
        
    except Exception as e:
        show_info(f"[vevo_timbre] Error: {str(e)}")
        # Clean up temporary files on error
        if 'src_path' in locals():
            try:
                os.unlink(src_path)
            except:
                pass
        if 'ref_path' in locals():
            try:
                os.unlink(ref_path)
            except:
                pass
        if 'out_path' in locals():
            try:
                os.unlink(out_path)
            except:
                pass
        raise


def vevo_timbre_from_paths(
    source_audio_path: str,
    reference_audio_path: str,
    output_path: Optional[str] = None,
    flow_matching_steps: int = 32,
    show_info=print
) -> str:
    """
    Perform timbre transfer using Vevo model from file paths.
    
    Args:
        source_audio_path: Path to source audio file
        reference_audio_path: Path to reference audio file
        output_path: Optional output path (if None, creates temporary file)
        flow_matching_steps: Number of flow matching steps for inference
        show_info: Logger function (default is `print`)
    
    Returns:
        str: Path to the output audio file
    """
    try:
        # Initialize pipeline if needed
        pipeline = _initialize_vevo_pipeline()
        show_info(f"[vevo_timbre] Pipeline initialized on device: {_device}")
        
        # Create output path if not provided
        if output_path is None:
            out_temp = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
            output_path = out_temp.name
            out_temp.close()
        
        # Perform inference
        start_time = time.time()
        show_info(f"[vevo_timbre] Starting timbre transfer with {flow_matching_steps} flow matching steps...")
        show_info(f"[vevo_timbre] Source: {source_audio_path}")
        show_info(f"[vevo_timbre] Reference: {reference_audio_path}")
        
        gen_audio = pipeline.inference_fm(
            src_wav_path=source_audio_path,
            timbre_ref_wav_path=reference_audio_path,
            flow_matching_steps=flow_matching_steps,
        )
        save_audio(gen_audio, output_path=output_path)
        
        end_time = time.time()
        latency = end_time - start_time
        show_info(f"[vevo_timbre] Timbre transfer completed in {latency:.2f} seconds")
        show_info(f"[vevo_timbre] Output saved to: {output_path}")
        
        return output_path
        
    except Exception as e:
        show_info(f"[vevo_timbre] Error: {str(e)}")
        raise
