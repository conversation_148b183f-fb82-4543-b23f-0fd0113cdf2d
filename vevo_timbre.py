import os
import shutil
import torch
import tempfile
import time
import asyncio
from typing import Optional, Union
from huggingface_hub import snapshot_download
from models.vc.vevo.vevo_utils import VevoInferencePipeline, save_audio

# Global variables for model initialization
_inference_pipeline = None
_device = None

async def _initialize_vevo_pipeline():
    """Initialize the Vevo inference pipeline if not already initialized (async version)."""
    global _inference_pipeline, _device

    if _inference_pipeline is not None:
        return _inference_pipeline

    # ===== Device =====
    _device = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")

    # Run model downloads and initialization in executor
    loop = asyncio.get_event_loop()

    def download_and_init():
        # ===== Model Setup =====
        tokenizer_dir = snapshot_download(
            repo_id="amphion/Vevo",
            repo_type="model",
            cache_dir="./ckpts/Vevo",
            allow_patterns=["tokenizer/vq8192/*"],
        )
        tokenizer_ckpt_path = os.path.join(tokenizer_dir, "tokenizer/vq8192")

        fmt_dir = snapshot_download(
            repo_id="amphion/Vevo",
            repo_type="model",
            cache_dir="./ckpts/Vevo",
            allow_patterns=["acoustic_modeling/Vq8192ToMels/*"],
        )
        fmt_cfg_path = "./models/vc/vevo/config/Vq8192ToMels.json"
        fmt_ckpt_path = os.path.join(fmt_dir, "acoustic_modeling/Vq8192ToMels")

        vocoder_dir = snapshot_download(
            repo_id="amphion/Vevo",
            repo_type="model",
            cache_dir="./ckpts/Vevo",
            allow_patterns=["acoustic_modeling/Vocoder/*"],
        )
        vocoder_cfg_path = "./models/vc/vevo/config/Vocoder.json"
        vocoder_ckpt_path = os.path.join(vocoder_dir, "acoustic_modeling/Vocoder")

        # ===== Inference Pipeline Init =====
        return VevoInferencePipeline(
            content_style_tokenizer_ckpt_path=tokenizer_ckpt_path,
            fmt_cfg_path=fmt_cfg_path,
            fmt_ckpt_path=fmt_ckpt_path,
            vocoder_cfg_path=vocoder_cfg_path,
            vocoder_ckpt_path=vocoder_ckpt_path,
            device=_device,
        )

    _inference_pipeline = await loop.run_in_executor(None, download_and_init)
    return _inference_pipeline


async def vevo_timbre_transfer(
    source_audio_file,  # File-like object (e.g., from UploadFile.file or open('audio.wav', 'rb'))
    reference_audio_file,  # File-like object (e.g., from UploadFile.file or open('audio.wav', 'rb'))
    flow_matching_steps: int = 32,
    show_info=print
) -> str:
    """
    Perform timbre transfer using Vevo model (async version).

    Args:
        source_audio_file: File-like object containing source audio (binary stream)
        reference_audio_file: File-like object containing reference audio (binary stream)
        flow_matching_steps: Number of flow matching steps for inference
        show_info: Logger function (default is `print`)

    Returns:
        str: Path to the output audio file
    """
    try:
        # Initialize pipeline if needed
        pipeline = await _initialize_vevo_pipeline()
        show_info(f"[vevo_timbre] Pipeline initialized on device: {_device}")

        # Get event loop for executor operations
        loop = asyncio.get_event_loop()

        # Create temporary files for input audio
        src_temp = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        ref_temp = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")

        # Copy uploaded files to temporary files in executor
        await loop.run_in_executor(None, shutil.copyfileobj, source_audio_file, src_temp)
        await loop.run_in_executor(None, shutil.copyfileobj, reference_audio_file, ref_temp)

        src_path = src_temp.name
        ref_path = ref_temp.name
        src_temp.close()
        ref_temp.close()

        show_info(f"[vevo_timbre] Saved source audio to: {src_path}")
        show_info(f"[vevo_timbre] Saved reference audio to: {ref_path}")

        # Create output file
        out_temp = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        out_path = out_temp.name
        out_temp.close()

        # Perform inference in executor
        start_time = time.time()
        show_info(f"[vevo_timbre] Starting timbre transfer with {flow_matching_steps} flow matching steps...")

        def run_inference():
            gen_audio = pipeline.inference_fm(
                src_wav_path=src_path,
                timbre_ref_wav_path=ref_path,
                flow_matching_steps=flow_matching_steps,
            )
            save_audio(gen_audio, output_path=out_path)
            return time.time()

        end_time = await loop.run_in_executor(None, run_inference)
        latency = end_time - start_time
        show_info(f"[vevo_timbre] Timbre transfer completed in {latency:.2f} seconds")
        show_info(f"[vevo_timbre] Output saved to: {out_path}")

        # Clean up temporary input files
        await loop.run_in_executor(None, os.unlink, src_path)
        await loop.run_in_executor(None, os.unlink, ref_path)

        return out_path

    except Exception as e:
        show_info(f"[vevo_timbre] Error: {str(e)}")
        # Clean up temporary files on error
        loop = asyncio.get_event_loop()
        if 'src_path' in locals():
            try:
                await loop.run_in_executor(None, os.unlink, src_path)
            except:
                pass
        if 'ref_path' in locals():
            try:
                await loop.run_in_executor(None, os.unlink, ref_path)
            except:
                pass
        if 'out_path' in locals():
            try:
                await loop.run_in_executor(None, os.unlink, out_path)
            except:
                pass
        raise


async def vevo_timbre_from_paths(
    source_audio_path: str,
    reference_audio_path: str,
    output_path: Optional[str] = None,
    flow_matching_steps: int = 32,
    show_info=print
) -> str:
    """
    Perform timbre transfer using Vevo model from file paths (async version).

    Args:
        source_audio_path: Path to source audio file
        reference_audio_path: Path to reference audio file
        output_path: Optional output path (if None, creates temporary file)
        flow_matching_steps: Number of flow matching steps for inference
        show_info: Logger function (default is `print`)

    Returns:
        str: Path to the output audio file
    """
    try:
        # Initialize pipeline if needed
        pipeline = await _initialize_vevo_pipeline()
        show_info(f"[vevo_timbre] Pipeline initialized on device: {_device}")

        # Create output path if not provided
        if output_path is None:
            out_temp = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
            output_path = out_temp.name
            out_temp.close()

        # Perform inference in executor
        loop = asyncio.get_event_loop()
        start_time = time.time()
        show_info(f"[vevo_timbre] Starting timbre transfer with {flow_matching_steps} flow matching steps...")
        show_info(f"[vevo_timbre] Source: {source_audio_path}")
        show_info(f"[vevo_timbre] Reference: {reference_audio_path}")

        def run_inference():
            gen_audio = pipeline.inference_fm(
                src_wav_path=source_audio_path,
                timbre_ref_wav_path=reference_audio_path,
                flow_matching_steps=flow_matching_steps,
            )
            save_audio(gen_audio, output_path=output_path)
            return time.time()

        end_time = await loop.run_in_executor(None, run_inference)
        latency = end_time - start_time
        show_info(f"[vevo_timbre] Timbre transfer completed in {latency:.2f} seconds")
        show_info(f"[vevo_timbre] Output saved to: {output_path}")

        return output_path

    except Exception as e:
        show_info(f"[vevo_timbre] Error: {str(e)}")
        raise
