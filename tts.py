from kokoro import KPipeline
import soundfile as sf
import numpy as np
import uuid

def generate_kokoro_tts(
    text: str,
    voice_choice: str = 'female_us',
) -> str:
    """
    Generate TTS audio using Kokoro with selectable voices.

    Args:
        text (str): Input text to synthesize.
        voice_choice (str): One of:
            'female_us', 'female_uk', 'male_us', 'male_uk'.
    Returns:
        str: Path to the saved WAV output file.
    """

    # Default map: choice -> Kokoro voice ID & lang code
    default_map = {
        'female_us':  ('af_bella', 'a'),
        'female_uk':  ('bf_emma',  'b'),
        'male_us':    ('am_adam',  'a'),
        'male_uk':    ('bm_george','b'),
    }

    voice_map = default_map

    if voice_choice not in voice_map:
        choices = ', '.join(voice_map.keys())
        raise ValueError(f"voice_choice must be one of: {choices}")

    voice_id, lang_code = voice_map[voice_choice]

    pipeline = KPipeline(lang_code=lang_code)

    # Generate audio with automatic splitting
    generator = pipeline(
        text,
        voice=voice_id,
        speed=1.0,
        split_pattern=r'\n+'
    )

    sample_rate = 24000
    segments = []

    for gs, ps, audio in generator:
        segments.append(audio)
        # insert 0.5-sec silence between segments
        silence = np.zeros(int(sample_rate * 0.5))
        segments.append(silence)

    final_audio = np.concatenate(segments)
    filename = f"kokoro_{voice_choice}_{uuid.uuid4().hex}.wav"
    sf.write(filename, final_audio, sample_rate)

    print(f"Saved TTS as: {filename}")
    return filename


text = "Here’s how you can convert your code into a clean function with a gender argument to select either a male or female voice. I've also added basic error handling and made it return the audio file path."
generate_kokoro_tts(text, voice_choice='male_us')
