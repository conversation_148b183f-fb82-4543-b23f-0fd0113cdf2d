from kokoro import KPipeline
import soundfile as sf
import numpy as np
import uuid
import asyncio

async def generate_kokoro_tts(
    text: str,
    voice_choice: str = 'female_us',
    show_info=print
) -> str:
    """
    Generate TTS audio using Kokoro with selectable voices (async version).

    Args:
        text (str): Input text to synthesize.
        voice_choice (str): One of:
            'female_us', 'female_uk', 'male_us', 'male_uk'.
        show_info: Logger function (default is `print`)
    Returns:
        str: Path to the saved WAV output file.
    """

    # Default map: choice -> Kokoro voice ID & lang code
    default_map = {
        'female_us':  ('af_bella', 'a'),
        'female_uk':  ('bf_emma',  'b'),
        'male_us':    ('am_adam',  'a'),
        'male_uk':    ('bm_george','b'),
    }

    voice_map = default_map

    if voice_choice not in voice_map:
        choices = ', '.join(voice_map.keys())
        raise ValueError(f"voice_choice must be one of: {choices}")

    voice_id, lang_code = voice_map[voice_choice]

    show_info(f"[kokoro_tts] Initializing pipeline with voice: {voice_choice} ({voice_id})")

    # Run CPU-intensive operations in executor
    loop = asyncio.get_event_loop()

    def generate_audio():
        pipeline = KPipeline(lang_code=lang_code)

        # Generate audio with automatic splitting
        generator = pipeline(
            text,
            voice=voice_id,
            speed=1.0,
            split_pattern=r'\n+'
        )

        sample_rate = 24000
        segments = []

        for gs, ps, audio in generator:
            segments.append(audio)
            # insert 0.5-sec silence between segments
            silence = np.zeros(int(sample_rate * 0.5))
            segments.append(silence)

        return np.concatenate(segments), sample_rate

    show_info(f"[kokoro_tts] Starting audio generation...")
    final_audio, sample_rate = await loop.run_in_executor(None, generate_audio)

    # Generate filename and save audio in executor
    filename = f"kokoro_{voice_choice}_{uuid.uuid4().hex}.wav"
    await loop.run_in_executor(None, sf.write, filename, final_audio, sample_rate)

    show_info(f"[kokoro_tts] Saved TTS as: {filename}")
    return filename


# Example usage (uncomment to test)
# import asyncio
#
# async def main():
#     text = "Here’s how you can convert your code into a clean function with a gender argument to select either a male or female voice. I've also added basic error handling and made it return the audio file path."
#     result = await generate_kokoro_tts(text, voice_choice='male_us')
#     print(f"Generated audio: {result}")
#
# if __name__ == "__main__":
#     asyncio.run(main())
