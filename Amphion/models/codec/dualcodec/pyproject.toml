[project]
name = "dualcodec"
version = "0.4.0"
description = "The DualCodec neural audio codec."
dependencies = [
    "transformers>=4.30.0",
    "descript-audiotools>=0.7.2",
    "huggingface_hub[cli]",
    "easydict",
    "torch",
    "torchaudio",
    "hydra-core",
    "einops",
    "safetensors",
    "cached_path",
]
packages = "dualcodec"
requires-python = ">=3.9"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.metadata]
allow-direct-references = true

[project.optional-dependencies]
tts = [
    "f5_tts",
    "accelerate>=1.0",
    "datasets",
    "openai-whisper",
    "cn2an",
]
