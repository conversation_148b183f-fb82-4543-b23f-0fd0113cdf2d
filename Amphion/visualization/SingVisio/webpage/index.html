<!--
    Copyright (c) 2023 Amphion.
    This source code is licensed under the MIT license found in the
    LICENSE file in the root directory of this source tree。
-->

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=1200px, initial-scale=1.0">
    <title>SingVisio: Visual Analytics of Diffusion Model for Singing Voice Conversion</title>
    <script>
        const baseLink = 'https://dsvc.openmmlab.org.cn'; // end without '/'
    </script>
    <!-- Load Tailwind CSS and D3.js -->
    <script src="./resources/tailwind.js"></script>
    <script src="./resources/d3.v4.min.js"></script>
    <script src="./resources/htl.min.js"></script>
    <script src="./resources/d3-scale-chromatic.v1.min.js"></script>
    <script src="./resources/d3-contour.v1.min.js"></script>
    <!-- Load the Guide driver -->
    <script src="./resources/driver.js.iife.min.js"></script>
    <link rel="stylesheet" href="./resources/driver.min.css">
    <!-- Config Tailwind CSS -->
    <script type="module">
        import cfg from "./tailwind.config.js";
        tailwind.config = cfg;
    </script>
    <style type="text/tailwindcss">
        @layer components {
        .btn-small {
            @apply px-3 py-2 text-xs font-medium text-center text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700
        }
        .btn {
            @apply text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800;
        }
        .btn-sec {
            @apply py-2.5 px-5 mr-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700
        }
        .select-select {
            @apply my-0 py-2 px-1 bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500
        }
        .select-label {
            @apply block text-sm font-medium text-gray-900 dark:text-white;
        }
        .card {
            @apply p-6 bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700
        }
        .card-title {
            @apply mb-2 text-base font-bold tracking-tight text-gray-900 dark:text-white
        }
        .timeline-point {
            @apply absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-900 dark:bg-gray-700
        }
        .timeline-title {
            @apply text-lg font-semibold text-gray-900 dark:text-white
        }
        .timeline-subtitle {
            @apply text-base font-normal text-gray-500 dark:text-gray-400
        }
        .small-input {
            @apply block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 sm:text-xs focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500;
        }
        .checkbox {
            @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600
        }
        .dropdown_button_text {
            @apply w-full text-xs font-normal text-gray-900 dark:text-white text-left
        }
        .dropdown_button {
            @apply btn-sec text-xs flex items-center w-full px-2 py-2 my-0 disabled:cursor-not-allowed disabled:opacity-50
        }
        }
        </style>
    <style>
        input.step-axis {
            outline: none;
            -webkit-appearance: none;
            background: #0000002b;
            height: 8px;
        }

        input.step-axis::-webkit-slider-thumb {
            -webkit-appearance: none;
            position: relative;
            width: 18px;
            height: 18px;
            background: url("./img/syllable.png") no-repeat;
            background-size: 18px;
            border-radius: 50%;
            cursor: pointer;
        }

        audio {
            outline: none;
            height: 34px;
        }

        /* make a input with two handles */
        .inputs {
            display: block;
            width: 100%;
            height: 10px;
            /* background-color: azure; */
        }

        .inputs input {
            position: absolute;
        }

        .inputs input::-webkit-slider-thumb {
            pointer-events: all;
            z-index: 2;
        }

        .inputs input::-webkit-slider-runnable-track {
            pointer-events: none;
            z-index: 1;
        }
    </style>
</head>

<body class="bg-gray-100 dark:bg-gray-900">
    <div id="alert"
        class="hidden fixed top-0 right-0 left-0 z-50 w-full h-[100vh] bg-black bg-opacity-50 justify-center items-center overflow-y-hidden">
        <div class="card flex flex-col min-w-[400px] max-w-2xl max-h-[80vh] p-0 overflow-hidden">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 id="alert_title" class="text-xl font-semibold text-gray-900 dark:text-white">
                    Title
                </h3>
                <button id="close_alert" type="button"
                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <div id="alert_text"
                class="p-4 md:p-5 space-y-4 text-base leading-relaxed text-gray-700 dark:text-gray-200 overflow-y-auto">
                Text
            </div>
            <!-- Modal footer -->
            <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                <button id="finish_alert" type="button"
                    class="ml-auto text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">OK</button>
            </div>
        </div>
    </div>
    <div class="bg-white dark:bg-gray-800 w-full py-4 px-6 border border-b border-gray-200 dark:border-gray-600">
        <div class="mx-auto max-w-[1490px] grid grid-cols-6 align-center items-center">
            <!-- <img class="dark:hidden" src="img/cuhksz_logo.png" alt="cuhksz logo" class="h-[40px]">
            <img class="hidden dark:block" src="img/cuhksz_logo_white.png" alt="cuhksz logo" class="h-[40px]"> -->
            <span class="col-span-1"></span>
            <span id="title" class="col-span-4 mx-auto font-[800] text-[20px] dark:text-white">SingVisio: Visual
                Analytics of Diffusion Model for Singing Voice Conversion</span>
            <!-- <span class="ml-auto mr-0 text-sm dark:text-white">Team: <i>Human Language Technology Lab,
                    CUHK-Shenzhen</i></span> -->
            <div class="flex">
                <button class="btn-small ml-auto" id="mode_change">Switch to _</button>
                <button class="btn-small ml-2" id="help">Help?</button>
            </div>
        </div>
    </div>
    <div class="max-w-[1500px] m-auto">
        <div class="flex flex-row items-start gap-0.5 py-3 p-1">
            <div class="w-[300px] flex flex-col flex-none">
                <div id="performance" class="card p-2 mb-2 flex flex-col flex-none relative">
                    <button class="absolute right-1 top-1 btn-small px-1.5 py-0.5 ml-auto rounded-full"
                        id="metrics_help">?</button>
                    <div class="flex flex-row">
                        <div id="histogram" class="flex-none"></div>
                        <div id="histogram2" class="flex-none"></div>
                    </div>
                    <span class="text-[12px] mx-auto dark:text-white">Metrics</span>
                </div>

                <div id="touch_map" class="card p-2 relative">
                    <button class="absolute right-1 top-1 btn-small px-1.5 py-0.5 ml-auto rounded-full"
                        id="projection_help">?</button>
                    <div class="flex mb-1 align-center items-center space-between dark:text-white">
                        <div class="ml-1 text-sm">Step: <span id="current_step_display_number"></span></div>
                        <div class="ml-auto flex mr-2">
                            <button class="btn-sec h-9 w-9 p-2.5 mb-0" id="reset_map">
                                <svg class="w-3.5 h-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 18 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M16 1v5h-5M2 19v-5h5m10-4a8 8 0 0 1-14.947 3.97M1 10a8 8 0 0 1 14.947-3.97" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div id="dataviz_axisZoom" class="flex flex-wrap border bg-white dark:bg-gray-800 relative"></div>
                </div>
            </div>
            <div class="w-full">
                <div id="step_preview" class="flex min-w-[500px] w-full bg-white dark:bg-gray-800 p-2 card mb-2">
                    <div class="mx-auto" id="preview_container">
                    </div>
                    <div class="mx-auto" id="preview_container2">
                    </div>
                    <div class="flex flex-col">
                        <button class="btn-sec" id="refreshpreview">
                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 18 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M16 1v5h-5M2 19v-5h5m10-4a8 8 0 0 1-14.947 3.97M1 10a8 8 0 0 1 14.947-3.97" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div id="mel_card_container" class="grid grid-cols-3 min-w-[915px] w-full gap-1 justify-items-center">
                </div>
                <div id="tips">
                </div>
                <div id="tooltip" role="tooltip"
                    class="invisible absolute z-10 inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-[0.9] dark:bg-gray-700">
                    Tooltip content
                </div>
            </div>
            <div class="shrink-0 w-[180px]">

                <div class="card py-2 px-3 relative">
                    <button class="absolute right-1 top-1 btn-small px-1.5 py-0.5 ml-auto rounded-full"
                        id="control_help">?</button>
                    <div class="flex items-center">
                        <h5 class="card-title my-1 text-lg">Control Panel</h5>
                    </div>
                    <div class="flex flex-col w-full rounded-lg gap-0.5" id="control_panel">
                        <div>
                            <label for="mode_id" class="select-label">Display Mode</label>
                            <select id="mode_id" class="select-select"></select>
                        </div>
                        <div>
                            <label for="sourcesinger_id" class="select-label">Source Singer</label>
                            <button id="sourcesinger_id" class="dropdown_button" type="button">
                                <span class="dropdown_button_text" id="sourcesinger_id_text">Choose Singer</span> <svg
                                    class="w-2.5 h-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 10 6">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 1 4 4 4-4" />
                                </svg>
                            </button>
                            <!-- Dropdown menu -->
                            <div id="sourcesinger_id_dropdown"
                                class="absolute z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
                                </ul>
                            </div>
                        </div>
                        <div>
                            <label for="song_id" class="select-label">Song</label>
                            <button id="song_id" class="dropdown_button" type="button">
                                <span class="dropdown_button_text" id="song_id_text">Choose Song</span> <svg
                                    class="w-2.5 h-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 10 6">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 1 4 4 4-4" />
                                </svg>
                            </button>
                            <!-- Dropdown menu -->
                            <div id="song_id_dropdown"
                                class="absolute z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
                                </ul>
                            </div>
                        </div>
                        <div>
                            <label for="target_id" class="select-label">Target Singer</label>
                            <button id="target_id" class="dropdown_button" type="button">
                                <span class="dropdown_button_text" id="target_id_text">Target Singer</span> <svg
                                    class="w-2.5 h-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 10 6">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 1 4 4 4-4" />
                                </svg>
                            </button>
                            <!-- Dropdown menu -->
                            <div id="target_id_dropdown"
                                class="absolute z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
                                </ul>
                            </div>
                        </div>
                        <div class="relative">
                            <label for="pic_id" class="select-label">Projection Embedding</label>
                            <select id="pic_id" class="select-select"></select>
                        </div>
                        <div class="relative" id="components">
                            <label for="components" class="select-label">Components</label>
                            <div class="flex flex-col gap-0.5">
                                <div class="flex items-center">
                                    <input id="components_pitch" type="checkbox" checked class="checkbox">
                                    <label for="components_pitch"
                                        class="ml-1 text-[0.775rem] font-normal text-gray-900 dark:text-gray-300">F0
                                        contour</label>
                                </div>
                                <div class="flex items-start">
                                    <input id="components_frequncy" type="checkbox" checked class="checkbox">
                                    <div class="flex flex-col gap-0.5 grow">
                                        <label for="components_frequncy"
                                            class="ml-1 mb-1 text-[0.775rem] font-normal text-gray-900 dark:text-gray-300">Frequency</label>
                                        <div class="flex inputs w-full">
                                            <input id="inputs_min" type="range"
                                                class="h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                                                value="0" min="0">
                                            <input id="inputs_max" type="range"
                                                class="h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                                                value="100" max="100">
                                        </div>
                                        <div class="flex w-full">
                                            <span id="inputs_left" class="ml-1 mr-auto text-[0.7rem] font-normal text-gray-900 dark:text-white">0</span>
                                            <span id="inputs_right" class="mr-2 ml-auto text-[0.7rem] font-normal text-gray-900 dark:text-white">100</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <input id="sampling_steps" type="checkbox" class="checkbox">
                                    <div class="flex flex-col grow">
                                        <label for="sampling_steps"
                                            class="ml-1 text-[0.775rem] font-normal text-gray-900 dark:text-gray-300">Sampling
                                            steps</label>
                                        <div class="flex flex-row h-[32px]">
                                            <span class="my-auto mx-1 text-[0.775rem] font-normal text-gray-900 dark:text-white">Step count:</span>
                                            <input type="text"
                                                class="small-input flex-none w-[50px] text-center bg-white dark:bg-gray-800"
                                                id="sampling_num" value="100">
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    

                    <div id="step_axis">
                        <label for="range" class="select-label">Step Axis</label>

                        <div
                            class="items-center w-full rounded-lg bg-gray-50 flex flex-row gap-2 px-2 py-0.5 border border-gray-300 dark:border-gray-600 dark:text-white dark:bg-gray-700">

                            <input class="step-axis my-2 w-full" id="range" type="range" min="0" max="999" value="0"
                                step="1">
                            <button class="btn-small" id="controls">
                                <svg id="icon_play" style="display: none" class="w-3 h-3" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 14 16">
                                    <path
                                        d="M0 .984v14.032a1 1 0 0 0 1.506.845l12.006-7.016a.974.974 0 0 0 0-1.69L1.506.139A1 1 0 0 0 0 .984Z" />
                                </svg>
                                <svg id="icon_stop" class="w-3 h-3" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 12 16">
                                    <path
                                        d="M3 0H2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm7 0H9a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Z" />
                                </svg>
                            </button>

                        </div>
                        <div class="flex gap-1 mt-2">
                            <span class="my-auto mr-1 text-sm font-medium text-gray-900 dark:text-white">Step:</span>
                            <input type="text"
                                class="small-input flex-none w-[60px] text-center bg-white dark:bg-gray-800" id="value">
                            <button class="btn-small" id="add_preview">
                                Pin
                            </button>
                        </div>
                    </div>

                </div>
                </div>
            </div>
        </div>

    </div>

    <script src="./resources/init.js"></script>
    <script src="./resources/function.js"></script>
    <script src="./resources/event.js"></script>
    <script>
        initConfig('./config/default.json')
    </script>
</body>