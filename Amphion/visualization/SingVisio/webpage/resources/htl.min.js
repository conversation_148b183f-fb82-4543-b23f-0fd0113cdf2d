// https://github.com/observablehq/htl v0.3.1 Copyright 2019-2021 Observable, Inc.
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).htl={})}(this,(function(e){"use strict";function t(e){const t=document.createElement("template");return t.innerHTML=e,document.importNode(t.content,!0)}function n(e){const t=document.createElementNS("http://www.w3.org/2000/svg","g");return t.innerHTML=e,t}const r=Object.assign(T(t,(e=>{if(null===e.firstChild)return null;if(e.firstChild===e.lastChild)return e.removeChild(e.firstChild);const t=document.createElement("span");return t.appendChild(e),t})),{fragment:T(t,(e=>e))}),a=Object.assign(T(n,(e=>null===e.firstChild?null:e.firstChild===e.lastChild?e.removeChild(e.firstChild):e)),{fragment:T(n,(e=>{const t=document.createDocumentFragment();for(;e.firstChild;)t.appendChild(e.firstChild);return t}))}),s=60,i=62,o=47,c=45,l=33,f=61,u=10,d=11,p=12,b=13,h=14,k=17,g=22,m=23,w=26,x="http://www.w3.org/2000/svg",C="http://www.w3.org/1999/xlink",y="http://www.w3.org/XML/1998/namespace",v="http://www.w3.org/2000/xmlns/",A=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map((e=>[e.toLowerCase(),e]))),N=new Map([["xlink:actuate",C],["xlink:arcrole",C],["xlink:href",C],["xlink:role",C],["xlink:show",C],["xlink:title",C],["xlink:type",C],["xml:lang",y],["xml:space",y],["xmlns",v],["xmlns:xlink",v]]);function T(e,t){return function({raw:n}){let r,a,x,C,y=1,v="",A=0;for(let e=0,t=arguments.length;e<t;++e){const t=n[e];if(e>0){const r=arguments[e];switch(y){case w:if(null!=r){const e=`${r}`;if(E(a))v+=e.replace(/[<]/g,L);else{if(new RegExp(`</${a}[\\s>/]`,"i").test(v.slice(-a.length-2)+e))throw new Error("unsafe raw text");v+=e}}break;case 1:null==r||(r instanceof Node||"string"!=typeof r&&r[Symbol.iterator]||/(?:^|>)$/.test(n[e-1])&&/^(?:<|$)/.test(t)?(v+="\x3c!--::"+e+"--\x3e",A|=128):v+=`${r}`.replace(/[<&]/g,L));break;case 9:{let a;if(y=p,/^[\s>]/.test(t)){if(null==r||!1===r){v=v.slice(0,x-n[e-1].length);break}if(!0===r||""==(a=`${r}`)){v+="''";break}if("style"===n[e-1].slice(x,C)&&M(r)||"function"==typeof r){v+="::"+e,A|=1;break}}if(void 0===a&&(a=`${r}`),""===a)throw new Error("unsafe unquoted empty string");v+=a.replace(/^['"]|[\s>&]/g,L);break}case p:v+=`${r}`.replace(/[\s>&]/g,L);break;case d:v+=`${r}`.replace(/['&]/g,L);break;case u:v+=`${r}`.replace(/["&]/g,L);break;case 6:if(M(r)){v+="::"+e+"=''",A|=1;break}throw new Error("invalid binding");case k:break;default:throw new Error("invalid binding")}}for(let e=0,n=t.length;e<n;++e){const n=t.charCodeAt(e);switch(y){case 1:n===s&&(y=2);break;case 2:n===l?y=25:n===o?y=3:S(n)?(r=e,a=void 0,y=4,--e):63===n?(y=5,--e):(y=1,--e);break;case 3:S(n)?(y=4,--e):n===i?y=1:(y=5,--e);break;case 4:U(n)?(y=6,a=j(t,r,e)):n===o?y=h:n===i&&(a=j(t,r,e),y=$(a)?w:1);break;case 6:U(n)||(n===o||n===i?(y=7,--e):n===f?(y=8,x=e+1,C=void 0):(y=8,--e,x=e+1,C=void 0));break;case 8:U(n)||n===o||n===i?(y=7,--e,C=e):n===f&&(y=9,C=e);break;case 7:U(n)||(n===o?y=h:n===f?y=9:n===i?y=$(a)?w:1:(y=8,--e,x=e+1,C=void 0));break;case 9:U(n)||(34===n?y=u:39===n?y=d:n===i?y=$(a)?w:1:(y=p,--e));break;case u:34===n&&(y=b);break;case d:39===n&&(y=b);break;case p:U(n)?y=6:n===i&&(y=$(a)?w:1);break;case b:U(n)?y=6:n===o?y=h:n===i?y=$(a)?w:1:(y=6,--e);break;case h:n===i?y=1:(y=6,--e);break;case 5:n===i&&(y=1);break;case 15:n===c?y=16:n===i?y=1:(y=k,--e);break;case 16:n===c?y=m:n===i?y=1:(y=k,--e);break;case k:n===s?y=18:n===c&&(y=g);break;case 18:n===l?y=19:n!==s&&(y=k,--e);break;case 19:n===c?y=20:(y=k,--e);break;case 20:n===c?y=21:(y=m,--e);break;case 21:y=m,--e;break;case g:n===c?y=m:(y=k,--e);break;case m:n===i?y=1:n===l?y=24:n!==c&&(y=k,--e);break;case 24:n===c?y=g:n===i?y=1:(y=k,--e);break;case 25:n===c&&t.charCodeAt(e+1)===c?(y=15,++e):(y=5,--e);break;case w:n===s&&(y=27);break;case 27:n===o?y=28:(y=w,--e);break;case 28:S(n)?(r=e,y=29,--e):(y=w,--e);break;case 29:U(n)&&a===j(t,r,e)?y=6:n===o&&a===j(t,r,e)?y=h:n===i&&a===j(t,r,e)?y=1:S(n)||(y=w,--e);break;default:y=void 0}}v+=t}const N=e(v),T=document.createTreeWalker(N,A,null,!1),R=[];for(;T.nextNode();){const e=T.currentNode;switch(e.nodeType){case 1:{const t=e.attributes;for(let n=0,r=t.length;n<r;++n){const{name:a,value:s}=t[n];if(/^::/.test(a)){const t=arguments[+a.slice(2)];P(e,a),--n,--r;for(const n in t){const r=t[n];null==r||!1===r||("function"==typeof r?e[n]=r:"style"===n&&M(r)?B(e[n],r):O(e,n,!0===r?"":r))}}else if(/^::/.test(s)){const t=arguments[+s.slice(2)];P(e,a),--n,--r,"function"==typeof t?e[a]=t:B(e[a],t)}}break}case 8:if(/^::/.test(e.data)){const t=e.parentNode,n=arguments[+e.data.slice(2)];if(n instanceof Node)t.insertBefore(n,e);else if("string"!=typeof n&&n[Symbol.iterator])if(n instanceof NodeList||n instanceof HTMLCollection)for(let r=n.length-1,a=e;r>=0;--r)a=t.insertBefore(n[r],a);else for(const r of n)null!=r&&t.insertBefore(r instanceof Node?r:document.createTextNode(r),e);else t.insertBefore(document.createTextNode(n),e);R.push(e)}}}for(const e of R)e.parentNode.removeChild(e);return t(N)}}function L(e){return`&#${e.charCodeAt(0).toString()};`}function S(e){return 65<=e&&e<=90||97<=e&&e<=122}function U(e){return 9===e||10===e||12===e||32===e||13===e}function M(e){return e&&e.toString===Object.prototype.toString}function $(e){return"script"===e||"style"===e||E(e)}function E(e){return"textarea"===e||"title"===e}function j(e,t,n){return e.slice(t,n).toLowerCase()}function O(e,t,n){e.namespaceURI===x&&(t=t.toLowerCase(),t=A.get(t)||t,N.has(t))?e.setAttributeNS(N.get(t),t,n):e.setAttribute(t,n)}function P(e,t){e.namespaceURI===x&&(t=t.toLowerCase(),t=A.get(t)||t,N.has(t))?e.removeAttributeNS(N.get(t),t):e.removeAttribute(t)}function B(e,t){for(const n in t){const r=t[n];n.startsWith("--")?e.setProperty(n,r):e[n]=r}}e.html=r,e.svg=a,e.version="0.3.1",Object.defineProperty(e,"__esModule",{value:!0})}));
